<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Optimisateur de Coupe de Fer - Méthode CPBO</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #2d3748;
            overflow: hidden;
        }

        .app-container {
            height: 100vh;
            display: grid;
            grid-template-areas: 
                "header header header header"
                "left center center right"
                "left center center right";
            grid-template-rows: 80px 1fr 1fr;
            grid-template-columns: 300px 1fr 1fr 320px;
            gap: 2px;
        }

        /* ZONE HAUT - Tableau de bord */
        .header {
            grid-area: header;
            background: linear-gradient(135deg, #1a365d 0%, #2c5282 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 20px;
            font-weight: 600;
        }

        .kpi-container {
            display: flex;
            gap: 30px;
            align-items: center;
        }

        .kpi-item {
            text-align: center;
        }

        .kpi-value {
            font-size: 24px;
            font-weight: bold;
            color: #0d8b68;
        }

        .kpi-label {
            font-size: 11px;
            opacity: 0.8;
            margin-top: 2px;
        }

        .toolbar {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #0d8b68;
            color: white;
        }

        .btn-primary:hover {
            background: #0a6b52;
        }

        .btn-secondary {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
        }

        .btn-secondary:hover {
            background: rgba(255,255,255,0.3);
        }

        /* ZONE GAUCHE - Saisie données */
        .left-panel {
            grid-area: left;
            background: white;
            border-right: 1px solid #e2e8f0;
            padding: 20px;
            overflow-y: auto;
        }

        .section {
            margin-bottom: 25px;
        }

        .section h3 {
            color: #1a365d;
            font-size: 14px;
            margin-bottom: 15px;
            font-weight: 600;
            border-bottom: 2px solid #0d8b68;
            padding-bottom: 5px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 12px;
            font-weight: 500;
            color: #4a5568;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #cbd5e0;
            border-radius: 4px;
            font-size: 12px;
        }

        .pieces-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            font-size: 11px;
        }

        .pieces-table th {
            background: #1a365d;
            color: white;
            padding: 8px 6px;
            text-align: left;
            font-weight: 500;
        }

        .pieces-table td {
            padding: 6px;
            border-bottom: 1px solid #e2e8f0;
        }

        .pieces-table input {
            width: 100%;
            padding: 4px;
            border: 1px solid #cbd5e0;
            border-radius: 2px;
            font-size: 11px;
        }

        .add-piece {
            background: #0d8b68;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 3px;
            font-size: 11px;
            cursor: pointer;
            margin-top: 8px;
        }

        /* ZONE CENTRE - Visualisation */
        .center-panel {
            grid-area: center;
            background: white;
            padding: 20px;
            overflow-y: auto;
        }

        .visualization-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .visualization-header h3 {
            color: #1a365d;
            font-size: 16px;
        }

        .pattern-container {
            margin-bottom: 30px;
        }

        .pattern-header {
            background: #f7fafc;
            padding: 10px 15px;
            border-left: 4px solid #0d8b68;
            margin-bottom: 10px;
        }

        .pattern-title {
            font-size: 14px;
            font-weight: 600;
            color: #1a365d;
        }

        .pattern-stats {
            font-size: 11px;
            color: #718096;
            margin-top: 3px;
        }

        .bar-visualization {
            background: #f8f9fa;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 15px;
            position: relative;
            height: 60px;
            margin-bottom: 10px;
        }

        .bar-length {
            position: absolute;
            top: 15px;
            left: 15px;
            height: 30px;
            background: #e2e8f0;
            border-radius: 3px;
            display: flex;
            overflow: hidden;
        }

        .cut-piece {
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 10px;
            font-weight: 600;
            border-right: 1px solid white;
        }

        .cut-piece.used { background: #1a365d; }
        .cut-piece.reusable { background: #0d8b68; }
        .cut-piece.waste { background: #e53e3e; }

        .legend {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
        }

        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 2px;
        }

        .cutting-instructions {
            background: #f7fafc;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
        }

        .instruction-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
            font-size: 12px;
        }

        .instruction-number {
            background: #1a365d;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            font-weight: bold;
        }

        /* ZONE DROITE - Analytics */
        .right-panel {
            grid-area: right;
            background: white;
            border-left: 1px solid #e2e8f0;
            padding: 20px;
            overflow-y: auto;
        }

        .analytics-section {
            margin-bottom: 25px;
        }

        .analytics-section h4 {
            color: #1a365d;
            font-size: 14px;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .pie-chart {
            width: 150px;
            height: 150px;
            margin: 0 auto 15px;
            position: relative;
        }

        .efficiency-score {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            color: #0d8b68;
            margin-bottom: 10px;
        }

        .comparison-table {
            width: 100%;
            font-size: 11px;
            border-collapse: collapse;
        }

        .comparison-table th {
            background: #f7fafc;
            padding: 8px 6px;
            text-align: left;
            font-weight: 500;
        }

        .comparison-table td {
            padding: 6px;
            border-bottom: 1px solid #e2e8f0;
        }

        .improvement {
            color: #0d8b68;
            font-weight: 600;
        }

        .recommendations {
            background: #f0fff4;
            border-left: 4px solid #0d8b68;
            padding: 15px;
            border-radius: 0 6px 6px 0;
        }

        .recommendation-item {
            font-size: 12px;
            margin-bottom: 8px;
            display: flex;
            align-items: flex-start;
            gap: 8px;
        }

        .recommendation-icon {
            color: #0d8b68;
            font-weight: bold;
            margin-top: 1px;
        }

        /* Animations */
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .computing {
            animation: pulse 1.5s infinite;
        }

        /* Scrollbars */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: #cbd5e0;
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #a0aec0;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- ZONE HAUT - Tableau de bord -->
        <div class="header">
            <h1>🔧 Optimisateur CPBO - Coupe de Fer Industrielle</h1>
            
            <div class="kpi-container">
                <div class="kpi-item">
                    <div class="kpi-value" id="efficiency">94.2%</div>
                    <div class="kpi-label">Efficacité</div>
                </div>
                <div class="kpi-item">
                    <div class="kpi-value" id="savings">€2,847</div>
                    <div class="kpi-label">Économies</div>
                </div>
                <div class="kpi-item">
                    <div class="kpi-value" id="production">156</div>
                    <div class="kpi-label">Pièces/h</div>
                </div>
                <div class="kpi-item">
                    <div class="kpi-value" id="compute-time">1.2s</div>
                    <div class="kpi-label">Calcul</div>
                </div>
            </div>

            <div class="toolbar">
                <button class="btn btn-secondary" onclick="newProject()">📄 Nouveau</button>
                <button class="btn btn-primary" onclick="optimizePatterns()">⚡ Optimiser</button>
                <button class="btn btn-secondary" onclick="exportResults()">📊 Exporter</button>
            </div>
        </div>

        <!-- ZONE GAUCHE - Saisie données -->
        <div class="left-panel">
            <div class="section">
                <h3>🔩 Paramètres Barres Standards</h3>
                <div class="form-group">
                    <label>Longueur standard (mm)</label>
                    <select id="barLength">
                        <option value="6000">6000mm - Standard européen</option>
                        <option value="12000" selected>12000mm - Industriel</option>
                        <option value="18000">18000mm - Grande série</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Coût par mètre (€)</label>
                    <input type="number" id="costPerMeter" value="12.50" step="0.01">
                </div>
                <div class="form-group">
                    <label>Perte de coupe (mm)</label>
                    <input type="number" id="kerfWidth" value="3" min="1" max="10">
                </div>
                <div class="form-group">
                    <label>Chute minimum réutilisable (mm)</label>
                    <input type="number" id="minReusable" value="200" min="50">
                </div>
            </div>

            <div class="section">
                <h3>📋 Pièces Requises</h3>
                <table class="pieces-table" id="piecesTable">
                    <thead>
                        <tr>
                            <th>Long. (mm)</th>
                            <th>Qté</th>
                            <th>Priorité</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><input type="number" value="3500" min="100"></td>
                            <td><input type="number" value="8" min="1"></td>
                            <td>
                                <select>
                                    <option value="high">Haute</option>
                                    <option value="medium" selected>Moyenne</option>
                                    <option value="low">Basse</option>
                                </select>
                            </td>
                            <td><button onclick="removePiece(this)">❌</button></td>
                        </tr>
                        <tr>
                            <td><input type="number" value="2800" min="100"></td>
                            <td><input type="number" value="12" min="1"></td>
                            <td>
                                <select>
                                    <option value="high" selected>Haute</option>
                                    <option value="medium">Moyenne</option>
                                    <option value="low">Basse</option>
                                </select>
                            </td>
                            <td><button onclick="removePiece(this)">❌</button></td>
                        </tr>
                        <tr>
                            <td><input type="number" value="1200" min="100"></td>
                            <td><input type="number" value="15" min="1"></td>
                            <td>
                                <select>
                                    <option value="high">Haute</option>
                                    <option value="medium" selected>Moyenne</option>
                                    <option value="low">Basse</option>
                                </select>
                            </td>
                            <td><button onclick="removePiece(this)">❌</button></td>
                        </tr>
                    </tbody>
                </table>
                <button class="add-piece" onclick="addPiece()">➕ Ajouter Pièce</button>
                
                <div style="margin-top: 15px; font-size: 11px;">
                    <button class="btn btn-secondary" onclick="importCSV()">📥 Import CSV</button>
                    <button class="btn btn-secondary" onclick="exportCSV()">📤 Export CSV</button>
                </div>
            </div>
        </div>

        <!-- ZONE CENTRE - Visualisation -->
        <div class="center-panel">
            <div class="visualization-header">
                <h3>🎯 Plans de Coupe Optimisés</h3>
                <div style="font-size: 12px; color: #718096;">
                    Méthode CPBO - Pattern Recognition Avancée
                </div>
            </div>

            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color" style="background: #1a365d;"></div>
                    <span>Pièce utilisée</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #0d8b68;"></div>
                    <span>Chute réutilisable</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #e53e3e;"></div>
                    <span>Perte</span>
                </div>
            </div>

            <div id="patternsContainer">
                <!-- Pattern 1 -->
                <div class="pattern-container">
                    <div class="pattern-header">
                        <div class="pattern-title">Barre #1 - Pattern A (Efficacité: 96.8%)</div>
                        <div class="pattern-stats">3x 3500mm + 1x 1200mm = 11700mm | Chute: 297mm | Perte: 3mm</div>
                    </div>
                    <div class="bar-visualization">
                        <div class="bar-length" style="width: 480px;">
                            <div class="cut-piece used" style="width: 140px;">3500</div>
                            <div class="cut-piece used" style="width: 140px;">3500</div>
                            <div class="cut-piece used" style="width: 140px;">3500</div>
                            <div class="cut-piece used" style="width: 48px;">1200</div>
                            <div class="cut-piece reusable" style="width: 12px;">297</div>
                        </div>
                    </div>
                </div>

                <!-- Pattern 2 -->
                <div class="pattern-container">
                    <div class="pattern-header">
                        <div class="pattern-title">Barre #2 - Pattern B (Efficacité: 94.1%)</div>
                        <div class="pattern-stats">4x 2800mm = 11200mm | Chute: 797mm | Perte: 3mm</div>
                    </div>
                    <div class="bar-visualization">
                        <div class="bar-length" style="width: 480px;">
                            <div class="cut-piece used" style="width: 112px;">2800</div>
                            <div class="cut-piece used" style="width: 112px;">2800</div>
                            <div class="cut-piece used" style="width: 112px;">2800</div>
                            <div class="cut-piece used" style="width: 112px;">2800</div>
                            <div class="cut-piece reusable" style="width: 32px;">797</div>
                        </div>
                    </div>
                </div>

                <!-- Pattern 3 -->
                <div class="pattern-container">
                    <div class="pattern-header">
                        <div class="pattern-title">Barre #3 - Pattern C (Efficacité: 92.3%)</div>
                        <div class="pattern-stats">1x 2800mm + 7x 1200mm = 11200mm | Chute: 494mm | Perte: 21mm</div>
                    </div>
                    <div class="bar-visualization">
                        <div class="bar-length" style="width: 480px;">
                            <div class="cut-piece used" style="width: 112px;">2800</div>
                            <div class="cut-piece used" style="width: 48px;">1200</div>
                            <div class="cut-piece used" style="width: 48px;">1200</div>
                            <div class="cut-piece used" style="width: 48px;">1200</div>
                            <div class="cut-piece used" style="width: 48px;">1200</div>
                            <div class="cut-piece used" style="width: 48px;">1200</div>
                            <div class="cut-piece used" style="width: 48px;">1200</div>
                            <div class="cut-piece used" style="width: 48px;">1200</div>
                            <div class="cut-piece reusable" style="width: 32px;">494</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="cutting-instructions">
                <h4 style="color: #1a365d; margin-bottom: 15px; font-size: 14px;">📋 Instructions de Coupe Séquentielles</h4>
                <div class="instruction-item">
                    <div class="instruction-number">1</div>
                    <div>Barre 12m → Couper 3x 3500mm + 1x 1200mm (Attention: économiser chute 297mm)</div>
                </div>
                <div class="instruction-item">
                    <div class="instruction-number">2</div>
                    <div>Barre 12m → Couper 4x 2800mm (Chute 797mm → Stock réutilisable)</div>
                </div>
                <div class="instruction-item">
                    <div class="instruction-number">3</div>
                    <div>Barre 12m → Couper 1x 2800mm + 7x 1200mm (Chute 494mm → Stock)</div>
                </div>
                <div class="instruction-item">
                    <div class="instruction-number">4</div>
                    <div>Optimisation chutes: Utiliser stock (1291mm) pour 1x 1200mm supplémentaire</div>
                </div>
            </div>
        </div>

        <!-- ZONE DROITE - Analytics -->
        <div class="right-panel">
            <div class="analytics-section">
                <h4>📊 Efficacité Matérielle</h4>
                <div class="efficiency-score">94.2%</div>
                <div style="text-align: center; font-size: 12px; color: #718096; margin-bottom: 15px;">
                    Score d'optimisation CPBO
                </div>
                <canvas id="efficiencyChart" class="pie-chart"></canvas>
            </div>

            <div class="analytics-section">
                <h4>📈 Comparatif Avant/Après</h4>
                <table class="comparison-table">
                    <tr>
                        <th></th>
                        <th>Avant</th>
                        <th>Après</th>
                        <th>Gain</th>
                    </tr>
                    <tr>
                        <td>Barres utilisées</td>
                        <td>5</td>
                        <td>3</td>
                        <td class="improvement">-40%</td>
                    </tr>
                    <tr>
                        <td>Coût matériaux</td>
                        <td>€750</td>
                        <td>€450</td>
                        <td class="improvement">-€300</td>
                    </tr>
                    <tr>
                        <td>Déchets (mm)</td>
                        <td>3,240</td>
                        <td>27</td>
                        <td class="improvement">-99.2%</td>
                    </tr>
                    <tr>
                        <td>Temps de coupe</td>
                        <td>45min</td>
                        <td>28min</td>
                        <td class="improvement">-38%</td>
                    </tr>
                </table>
            </div>

            <div class="analytics-section">
                <h4>💡 Recommandations IA</h4>
                <div class="recommendations">
                    <div class="recommendation-item">
                        <span class="recommendation-icon">→</span>
                        <span>Grouper les commandes similaires pour optimiser les chutes sur 15 jours</span>
                    </div>
                    <div class="recommendation-item">
                        <span class="recommendation-icon">→</span>
                        <span>Stock de chutes actuel: 1,291mm disponible pour projets urgents</span>
                    </div>
                    <div class="recommendation-item">
                        <span class="recommendation-icon">→</span>
                        <span>Pattern B répétable pour commandes série 2800mm (ROI +23%)</span>
                    </div>
                    <div class="recommendation-item">
                        <span class="recommendation-icon">→</span>
                        <span>Négocier barres 15m avec fournisseur pour +8% d'efficacité</span>
                    </div>
                </div>
            </div>

            <div class="analytics-section">
                <h4>📑 Actions Rapides</h4>
                <div style="display: flex; flex-direction: column; gap: 8px;">
                    <button class="btn btn-primary" onclick="generatePDFReport()">📄 Rapport PDF</button>
                    <button class="btn btn-secondary" onclick="exportToERP()">🔄 Vers ERP</button>
                    <button class="btn btn-secondary" onclick="savePattern()">💾 Sauver Pattern</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let currentPatterns = [];
        let optimizationHistory = [];
        let wasteStock = 0;
        
        // Initialisation des graphiques
        function initializeCharts() {
            const canvas = document.getElementById('efficiencyChart');
            const ctx = canvas.getContext('2d');
            
            // Graphique camembert simple
            const centerX = 75;
            const centerY = 75;
            const radius = 60;
            
            // Segments du graphique
            const segments = [
                { value: 94.2, color: '#0d8b68', label: 'Utilisé' },
                { value: 4.1, color: '#fbb034', label: 'Chutes' },
                { value: 1.7, color: '#e53e3e', label: 'Pertes' }
            ];
            
            let currentAngle = -Math.PI / 2;
            
            segments.forEach(segment => {
                const sliceAngle = (segment.value / 100) * 2 * Math.PI;
                
                ctx.beginPath();
                ctx.moveTo(centerX, centerY);
                ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
                ctx.closePath();
                ctx.fillStyle = segment.color;
                ctx.fill();
                
                currentAngle += sliceAngle;
            });
        }
        
        // Algorithme CPBO - Complete Pattern-Based Optimization
        function optimizePatterns() {
            const computeTimeEl = document.getElementById('compute-time');
            const efficiencyEl = document.getElementById('efficiency');
            
            // Animation de calcul
            computeTimeEl.parentElement.classList.add('computing');
            
            setTimeout(() => {
                // Simulation de l'algorithme CPBO
                const pieces = getPiecesFromTable();
                const barLength = parseInt(document.getElementById('barLength').value);
                const kerfWidth = parseInt(document.getElementById('kerfWidth').value);
                const minReusable = parseInt(document.getElementById('minReusable').value);
                
                const patterns = generateOptimalPatterns(pieces, barLength, kerfWidth, minReusable);
                displayPatterns(patterns);
                updateKPIs(patterns);
                updateRecommendations(patterns);
                
                computeTimeEl.parentElement.classList.remove('computing');
                
                // Animation de réussite
                efficiencyEl.style.color = '#0d8b68';
                setTimeout(() => {
                    efficiencyEl.style.color = '#0d8b68';
                }, 1000);
                
            }, 1200);
        }
        
        // Algorithme de génération des patterns optimaux
        function generateOptimalPatterns(pieces, barLength, kerfWidth, minReusable) {
            const patterns = [];
            let remainingPieces = [...pieces];
            
            while (remainingPieces.some(p => p.quantity > 0)) {
                const pattern = findBestPattern(remainingPieces, barLength, kerfWidth, minReusable);
                if (pattern) {
                    patterns.push(pattern);
                    // Décrémenter les quantités
                    pattern.cuts.forEach(cut => {
                        const piece = remainingPieces.find(p => p.length === cut.length);
                        if (piece) piece.quantity -= cut.quantity;
                    });
                } else {
                    break;
                }
            }
            
            return patterns;
        }
        
        // Recherche du meilleur pattern pour une barre
        function findBestPattern(pieces, barLength, kerfWidth, minReusable) {
            let bestPattern = null;
            let bestEfficiency = 0;
            
            // Algorithme récursif de recherche de patterns
            const availablePieces = pieces.filter(p => p